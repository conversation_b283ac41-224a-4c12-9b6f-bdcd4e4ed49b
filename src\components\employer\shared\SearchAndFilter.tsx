import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { Button, Group, Select, TextInput } from "@mantine/core";
import { FaFilter, FaSearch, FaTimes } from "react-icons/fa";

interface FilterOption {
  value: string;
  label: string;
}

interface SearchAndFilterProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  filters?: {
    status?: {
      value: string;
      onChange: (value: string | null) => void;
      options: FilterOption[];
    };
    location?: {
      value: string;
      onChange: (value: string | null) => void;
      options: FilterOption[];
    };
    jobType?: {
      value: string;
      onChange: (value: string | null) => void;
      options: FilterOption[];
    };
    dateRange?: {
      value: string;
      onChange: (value: string | null) => void;
      options: FilterOption[];
    };
  };
  onClearFilters?: () => void;
  placeholder?: string;
  showFilterButton?: boolean;
}

export default function SearchAndFilter({
  searchValue,
  onSearchChange,
  filters,
  onClearFilters,
  placeholder = "Search jobs...",
  showFilterButton = true,
}: SearchAndFilterProps) {
  const hasActiveFilters = filters && Object.values(filters).some(filter => filter.value);

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <TextInput
        placeholder={placeholder}
        value={searchValue}
        onChange={(event) => onSearchChange(event.currentTarget.value)}
        leftSection={<FaSearch size={16} />}
        size="md"
        radius="md"
        className="w-full"
      />

      {/* Filters */}
      {filters && (
        <div
          className={useThemeClasses(
            "rounded-lg border border-gray-200 bg-gray-50 p-4",
            "rounded-lg border border-dark-4 bg-dark-6 p-4",
          )}
        >
          <div className="mb-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FaFilter size={14} className="text-primary-color" />
              <span className="text-sm font-medium">Filters</span>
            </div>
            {hasActiveFilters && onClearFilters && (
              <Button
                variant="subtle"
                size="xs"
                leftSection={<FaTimes size={12} />}
                onClick={onClearFilters}
                color="gray"
              >
                Clear All
              </Button>
            )}
          </div>

          <Group gap="md" className="flex-wrap">
            {filters.status && (
              <Select
                placeholder="Status"
                value={filters.status.value}
                onChange={filters.status.onChange}
                data={filters.status.options}
                clearable
                size="sm"
                className="min-w-32"
              />
            )}
            {filters.location && (
              <Select
                placeholder="Location"
                value={filters.location.value}
                onChange={filters.location.onChange}
                data={filters.location.options}
                clearable
                size="sm"
                className="min-w-32"
              />
            )}
            {filters.jobType && (
              <Select
                placeholder="Job Type"
                value={filters.jobType.value}
                onChange={filters.jobType.onChange}
                data={filters.jobType.options}
                clearable
                size="sm"
                className="min-w-32"
              />
            )}
            {filters.dateRange && (
              <Select
                placeholder="Date Range"
                value={filters.dateRange.value}
                onChange={filters.dateRange.onChange}
                data={filters.dateRange.options}
                clearable
                size="sm"
                className="min-w-32"
              />
            )}
          </Group>
        </div>
      )}
    </div>
  );
}
