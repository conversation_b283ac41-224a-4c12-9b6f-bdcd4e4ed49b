import { z } from "zod";

// Step 1: Basic Information validation
export const basicInformationSchema = z
  .object({
    jobTitle: z.string().min(2, "Job title must be at least 2 characters"),
    minSalary: z
      .number()
      .min(0, "Minimum salary must be a positive number")
      .optional(),
    maxSalary: z
      .number()
      .min(0, "Maximum salary must be a positive number")
      .optional(),
    currency: z.string().optional(),
    location: z.string().min(1, "Location is required"),
    jobType: z.string().min(1, "Job type is required"),
    requiredExperience: z.string().min(1, "Required experience is required"),
    jobCategory: z.string().min(1, "Job category is required"),
    showSalary: z.boolean().optional(),
  })
  .refine(
    (data) => {
      // If minSalary or maxSalary is provided, currency is required
      if (data.minSalary !== undefined || data.maxSalary !== undefined) {
        return !!data.currency;
      }
      return true;
    },
    {
      message: "Currency is required when salary is provided",
      path: ["currency"],
    },
  )
  .refine(
    (data) => {
      // If both minSalary and maxSalary are provided, maxSalary must be >= minSalary
      if (data.minSalary !== undefined && data.maxSalary !== undefined) {
        return data.maxSalary >= data.minSalary;
      }
      return true;
    },
    {
      message: "Maximum salary must be greater than or equal to minimum salary",
      path: ["maxSalary"],
    },
  );

// Step 2: Description validation
export const descriptionSchema = z.object({
  jobDescription: z
    .string()
    .min(10, "Description must be at least 10 characters"),
  jobRequirements: z
    .string()
    .min(10, "Requirements must be at least 10 characters"),
  jobTags: z.array(z.string()).optional(),
});

// Step 3: Additional Details validation
export const additionalDetailsSchema = z.object({
  benefits: z.array(z.string()).optional(),
  applicationDeadline: z.date().optional(),
  jobAttachment: z.instanceof(File).optional(),
});

// Helper function to validate a specific step
export function validateStep(step: number, formValues: any): { isValid: boolean; errors: string[] } {
  let schema: z.ZodSchema;
  let stepData: any;

  switch (step) {
    case 0: // Basic Information
      schema = basicInformationSchema;
      stepData = {
        jobTitle: formValues.jobTitle,
        minSalary: formValues.minSalary,
        maxSalary: formValues.maxSalary,
        currency: formValues.currency,
        location: formValues.location,
        jobType: formValues.jobType,
        requiredExperience: formValues.requiredExperience,
        jobCategory: formValues.jobCategory,
        showSalary: formValues.showSalary,
      };
      break;
    case 1: // Description
      schema = descriptionSchema;
      stepData = {
        jobDescription: formValues.jobDescription,
        jobRequirements: formValues.jobRequirements,
        jobTags: formValues.jobTags,
      };
      break;
    case 2: // Additional Details
      schema = additionalDetailsSchema;
      stepData = {
        benefits: formValues.benefits,
        applicationDeadline: formValues.applicationDeadline,
        jobAttachment: formValues.jobAttachment,
      };
      break;
    default:
      return { isValid: true, errors: [] };
  }

  try {
    schema.parse(stepData);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => err.message);
      return { isValid: false, errors };
    }
    return { isValid: false, errors: ["Validation failed"] };
  }
}

// Helper function to get required fields for a step
export function getRequiredFieldsForStep(step: number): string[] {
  switch (step) {
    case 0:
      return ["jobTitle", "location", "jobType", "requiredExperience", "jobCategory"];
    case 1:
      return ["jobDescription", "jobRequirements"];
    case 2:
      return []; // No required fields in step 3
    default:
      return [];
  }
}

// Helper function to check if specific fields have values
export function checkRequiredFields(formValues: any, requiredFields: string[]): { isValid: boolean; missingFields: string[] } {
  const missingFields: string[] = [];
  
  for (const field of requiredFields) {
    const value = formValues[field];
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      missingFields.push(field);
    }
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}
