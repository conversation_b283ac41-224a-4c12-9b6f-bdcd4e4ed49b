import { useThemeClasses } from "@/design-system/utils/theme-utils";
import useCreateJobForm from "@/hooks/employer/use-create-job-form";
import { validateStep } from "@/schemas/employer/create-job-step-schemas";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Text } from "@mantine/core";
import { useState } from "react";
import {
  FaArrowLeft,
  FaArrowRight,
  FaBriefcase,
  FaCheck,
  FaClipboardCheck,
  FaExclamationTriangle,
  FaFileAlt,
  FaGift,
} from "react-icons/fa";

// Import step components
import {
  AdditionalDetailsStep,
  BasicInformationStep,
  DescriptionStep,
  ReviewStep,
} from "../shared";

export default function CreateJobForm() {
  const { form, handleSubmit } = useCreateJobForm();
  const [questions, setQuestions] = useState<string[]>([""]);
  const [active, setActive] = useState(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showValidationAlert, setShowValidationAlert] = useState(false);

  const handleQuestionChange = (index: number, value: string) => {
    const newQuestions = [...questions];
    newQuestions[index] = value;
    setQuestions(newQuestions);
  };

  const addQuestion = () => {
    setQuestions([...questions, ""]);
  };

  const removeQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    setQuestions(newQuestions);
  };

  const addPredefinedQuestion = (question: string) => {
    setQuestions([...questions, question]);
  };

  const nextStep = () => {
    // Validate current step before proceeding
    const validation = validateStep(active, form.values);

    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      setShowValidationAlert(true);
      // Scroll to top to show the alert
      window.scrollTo({ top: 0, behavior: "smooth" });
      return;
    }

    // Clear any previous validation errors
    setValidationErrors([]);
    setShowValidationAlert(false);

    if (active < 3) {
      setActive((current) => current + 1);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const prevStep = () => {
    // Clear validation errors when going back
    setValidationErrors([]);
    setShowValidationAlert(false);

    if (active > 0) {
      setActive((current) => current - 1);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const onSubmitForm = (values: any) => {
    console.log("Form submitted:", values);
    handleSubmit(values);
  };

  return (
    <form
      noValidate
      onSubmit={form.onSubmit(onSubmitForm)}
      className="flex flex-col gap-8"
    >
      {/* Stepper */}
      <Card
        shadow="sm"
        radius="md"
        className={useThemeClasses(
          "border border-gray-200",
          "border border-dark-4",
        )}
      >
        <Stepper
          active={active}
          onStepClick={setActive}
          orientation="horizontal"
          color="blue"
          size="md"
          iconSize={32}
          className="px-2 py-4 md:px-4"
          styles={{
            stepBody: {
              marginTop: "8px",
            },
            step: {
              padding: "12px 8px",
              transition: "all 0.2s ease",
            },
            stepIcon: {
              borderWidth: "2px",
              transition: "all 0.2s ease",
            },
            separator: {
              marginLeft: "4px",
              marginRight: "4px",
              height: "2px",
            },
          }}
        >
          <Stepper.Step
            label="Basic Information"
            description="Job details"
            icon={<FaBriefcase size={18} />}
            completedIcon={<FaCheck size={18} />}
          />
          <Stepper.Step
            label="Description"
            description="Job description & requirements"
            icon={<FaFileAlt size={18} />}
            completedIcon={<FaCheck size={18} />}
          />
          <Stepper.Step
            label="Additional Details"
            description="Benefits & perks"
            icon={<FaGift size={18} />}
            completedIcon={<FaCheck size={18} />}
          />
          <Stepper.Step
            label="Review & Submit"
            description="Preview and publish"
            icon={<FaClipboardCheck size={18} />}
            completedIcon={<FaCheck size={18} />}
          />
        </Stepper>
      </Card>

      {/* Validation Alert */}
      {showValidationAlert && validationErrors.length > 0 && (
        <Alert
          icon={<FaExclamationTriangle size={16} />}
          title="Please complete all required fields"
          color="red"
          radius="md"
          variant="light"
          withCloseButton
          onClose={() => setShowValidationAlert(false)}
          className="mb-6"
        >
          <Text size="sm">
            Please fill in the following required fields before proceeding:
          </Text>
          <ul className="mt-2 ml-4 list-disc">
            {validationErrors.map((error, index) => (
              <li key={index} className="text-sm">
                {error}
              </li>
            ))}
          </ul>
        </Alert>
      )}

      {/* Step Content */}
      <Card
        shadow="sm"
        radius="md"
        className={useThemeClasses(
          "border border-gray-200 p-0",
          "border border-dark-4 p-0",
        )}
      >
        <div className="p-6 md:p-8">
          {active === 0 && <BasicInformationStep form={form} />}
          {active === 1 && <DescriptionStep form={form} />}
          {active === 2 && (
            <AdditionalDetailsStep
              form={form}
              questions={questions}
              handleQuestionChange={handleQuestionChange}
              addQuestion={addQuestion}
              removeQuestion={removeQuestion}
              addPredefinedQuestion={addPredefinedQuestion}
            />
          )}
          {active === 3 && <ReviewStep form={form} />}
        </div>
      </Card>

      {/* Navigation Buttons */}
      <Card
        shadow="sm"
        radius="md"
        className={useThemeClasses(
          "border border-gray-200",
          "border border-dark-4",
        )}
      >
        <div className="flex items-center justify-between p-4">
          {active > 0 ? (
            <Button
              variant="outline"
              onClick={prevStep}
              leftSection={<FaArrowLeft size={14} />}
              radius="md"
              color="gray"
              className="transition-all duration-200"
            >
              Previous Step
            </Button>
          ) : (
            <div></div>
          )}

          {active < 3 ? (
            <Button
              onClick={nextStep}
              rightSection={<FaArrowRight size={14} />}
              radius="md"
              color="primary"
              className="transition-all duration-200"
            >
              Next Step
            </Button>
          ) : (
            <Button
              type="submit"
              color="teal"
              rightSection={<FaCheck size={14} />}
              radius="md"
              className="transition-all duration-200"
              size="md"
            >
              Submit Job
            </Button>
          )}
        </div>
      </Card>
    </form>
  );
}
