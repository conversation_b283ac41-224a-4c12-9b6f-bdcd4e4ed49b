import { Page<PERSON>ontainer, PageHeading } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import useProfileForm from "@/hooks/employer/use-profile-form";
import {
  ActionIcon,
  Avatar,
  Button,
  Card,
  FileInput,
  Grid,
  Group,
  Modal,
  RingProgress,
  Select,
  Tabs,
  Text,
  TextInput,
  Textarea,
  Title,
  Tooltip,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaCamera,
  FaCheck,
  FaCheckCircle,
  FaEnvelope,
  FaFacebook,
  FaGlobe,
  FaInstagram,
  FaLinkedin,
  FaMapMarkerAlt,
  FaPhone,
  FaSave,
  FaTrash,
  FaTwitter,
  FaUpload,
  FaUser,
} from "react-icons/fa";
import { Link } from "react-router";

export default function EmployerProfilePage() {
  // Mock profile completion percentage
  const profileCompletion = 85;

  // Get form from hook
  const { form } = useProfileForm();

  // State for active tab
  const [activeTab, setActiveTab] = useState<string | null>("company");

  // Company logo state
  const [companyLogoPreview, setCompanyLogoPreview] = useState<string | null>(
    null,
  );

  // Modal states
  const [logoModalOpened, { open: openLogoModal, close: closeLogoModal }] =
    useDisclosure(false);

  // Handle company logo upload
  const handleLogoChange = (file: File | null) => {
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setCompanyLogoPreview(imageUrl);
      closeLogoModal();

      notifications.show({
        title: "Company Logo Updated",
        message: "Your company logo has been successfully updated",
        color: "green",
        icon: <FaCheck />,
      });
    }
  };

  // Handle company logo removal
  const handleRemoveLogo = () => {
    setCompanyLogoPreview(null);

    notifications.show({
      title: "Company Logo Removed",
      message: "Your company logo has been removed",
      color: "red",
    });
  };

  // Handle form submission
  const onSubmit = (values: any) => {
    console.log(values);
    // Save profile data
    notifications.show({
      title: "Profile Updated",
      message: "Your company profile has been successfully updated",
      color: "green",
      icon: <FaCheck />,
    });
  };

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Profile" }]}
      variant="employer"
      className="pb-12"
    >
      <PageHeading
        title="Company Profile"
        subtitle="Manage your company information and preferences"
        variant="employer"
      />

      {/* Hero Section */}
      <Card withBorder radius="md" className="mb-8 overflow-hidden" p={0}>
        <div className="relative">
          {/* Background gradient */}
          <div
            className={useThemeClasses(
              "absolute inset-0 bg-gradient-to-r from-blue-50 via-indigo-50 to-blue-100",
              "absolute inset-0 bg-gradient-to-r from-dark-6 via-dark-5 to-dark-6",
            )}
          ></div>

          <div className="relative p-6 md:p-8">
            <Grid>
              <Grid.Col span={{ base: 12, sm: 8 }}>
                <Group align="flex-start" className="mb-4">
                  <div className="relative">
                    <Avatar
                      size={120}
                      radius="md"
                      color="blue"
                      className="border-4 border-white shadow-md"
                      src={companyLogoPreview}
                    >
                      {!companyLogoPreview && form.values.companyName.charAt(0)}
                    </Avatar>
                    <div className="absolute -right-2 -bottom-2 flex gap-1">
                      <Tooltip label="Change company logo">
                        <ActionIcon
                          className={useThemeClasses(
                            "border border-gray-200 bg-white shadow-sm",
                            "border border-dark-4 bg-dark-5 shadow-dark-sm",
                          )}
                          radius="xl"
                          variant="filled"
                          size="md"
                          color="blue"
                          onClick={openLogoModal}
                        >
                          <FaCamera size={14} />
                        </ActionIcon>
                      </Tooltip>
                      {companyLogoPreview && (
                        <Tooltip label="Remove company logo">
                          <ActionIcon
                            className={useThemeClasses(
                              "border border-gray-200 bg-white shadow-sm",
                              "border border-dark-4 bg-dark-5 shadow-dark-sm",
                            )}
                            radius="xl"
                            variant="filled"
                            size="md"
                            color="red"
                            onClick={handleRemoveLogo}
                          >
                            <FaTrash size={14} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                    </div>
                  </div>

                  <div>
                    <Title order={1} className="text-2xl font-bold">
                      {form.values.companyName}
                    </Title>

                    <Text
                      size="lg"
                      className={useThemeClasses(
                        "mb-2 text-gray-700",
                        "mb-2 text-gray-300",
                      )}
                    >
                      {form.values.industry}
                    </Text>

                    <Group gap="lg" className="mt-3">
                      <Group gap="xs">
                        <FaEnvelope className="text-blue-500" />
                        <Text size="sm">{form.values.email}</Text>
                      </Group>

                      <Group gap="xs">
                        <FaPhone className="text-blue-500" />
                        <Text size="sm">{form.values.phone}</Text>
                      </Group>

                      <Group gap="xs">
                        <FaMapMarkerAlt className="text-blue-500" />
                        <Text size="sm">{form.values.location}</Text>
                      </Group>
                    </Group>
                  </div>
                </Group>
              </Grid.Col>

              <Grid.Col span={{ base: 12, sm: 4 }}>
                <Card
                  withBorder
                  radius="md"
                  className={useThemeClasses(
                    "bg-white shadow-sm",
                    "bg-dark-6 shadow-dark-lg",
                  )}
                >
                  <Text fw={700} ta="center" className="mb-2">
                    Profile Completion
                  </Text>
                  <Group justify="center" className="mb-2">
                    <RingProgress
                      size={80}
                      thickness={8}
                      roundCaps
                      sections={[{ value: profileCompletion, color: "blue" }]}
                      label={
                        <Text ta="center" size="sm" fw={700}>
                          {profileCompletion}%
                        </Text>
                      }
                    />
                  </Group>
                  <Text size="sm" c="dimmed" ta="center">
                    Complete your profile to attract more candidates
                  </Text>
                  {profileCompletion >= 100 && (
                    <Group gap="xs" mt={4} justify="center">
                      <FaCheckCircle size={14} className="text-green-500" />
                      <Text size="xs" c="green">
                        Your profile is complete!
                      </Text>
                    </Group>
                  )}
                </Card>
              </Grid.Col>
            </Grid>

            {/* Social Media Quick Links */}
            <Group className="mt-4">
              <Group ml="auto" gap="xs">
                {form.values.linkedin && (
                  <Tooltip label="LinkedIn Profile">
                    <ActionIcon
                      component={Link}
                      to={form.values.linkedin}
                      target="_blank"
                      variant="light"
                      color="blue"
                      size="lg"
                    >
                      <FaLinkedin size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                {form.values.twitter && (
                  <Tooltip label="Twitter Profile">
                    <ActionIcon
                      component={Link}
                      to={form.values.twitter}
                      target="_blank"
                      variant="light"
                      color="cyan"
                      size="lg"
                    >
                      <FaTwitter size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                {form.values.facebook && (
                  <Tooltip label="Facebook Page">
                    <ActionIcon
                      component={Link}
                      to={form.values.facebook}
                      target="_blank"
                      variant="light"
                      color="indigo"
                      size="lg"
                    >
                      <FaFacebook size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                {form.values.instagram && (
                  <Tooltip label="Instagram Profile">
                    <ActionIcon
                      component={Link}
                      to={form.values.instagram}
                      target="_blank"
                      variant="light"
                      color="pink"
                      size="lg"
                    >
                      <FaInstagram size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                {form.values.website && (
                  <Tooltip label="Company Website">
                    <ActionIcon
                      component={Link}
                      to={form.values.website}
                      target="_blank"
                      variant="light"
                      color="gray"
                      size="lg"
                    >
                      <FaGlobe size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}
              </Group>
            </Group>
          </div>
        </div>
      </Card>

      {/* Tabbed Content */}
      <form onSubmit={form.onSubmit(onSubmit)}>
        <Tabs value={activeTab} onChange={setActiveTab} className="mb-6">
          <Tabs.List grow>
            <Tabs.Tab value="company" leftSection={<FaBuilding size={16} />}>
              Company Information
            </Tabs.Tab>
            <Tabs.Tab value="social" leftSection={<FaGlobe size={16} />}>
              Social Media
            </Tabs.Tab>
            <Tabs.Tab value="details" leftSection={<FaBriefcase size={16} />}>
              Company Details
            </Tabs.Tab>
          </Tabs.List>

          {/* Company Information Tab */}
          <Tabs.Panel value="company" pt="md">
            <Card
              withBorder
              radius="md"
              className={useThemeClasses(
                "p-6 shadow-sm",
                "p-6 shadow-dark-sm bg-dark-7",
              )}
            >
              <Title
                order={3}
                className={useThemeClasses(
                  "mb-4 text-xl font-semibold text-blue-800",
                  "mb-4 text-xl font-semibold text-blue-300",
                )}
              >
                Company Information
              </Title>

              <Grid gutter="md">
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Company Name"
                    placeholder="Enter your company name"
                    required
                    leftSection={
                      <FaBuilding size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("companyName")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Contact Person"
                    placeholder="Enter contact person's name"
                    required
                    leftSection={<FaUser size={16} className="text-gray-500" />}
                    {...form.getInputProps("contactPerson")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Email"
                    placeholder="Enter company email"
                    required
                    leftSection={
                      <FaEnvelope size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("email")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Phone"
                    placeholder="Enter company phone number"
                    required
                    leftSection={
                      <FaPhone size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("phone")}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <TextInput
                    label="Website"
                    placeholder="Enter company website URL"
                    leftSection={
                      <FaGlobe size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("website")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Industry"
                    placeholder="Enter your industry"
                    leftSection={
                      <FaBriefcase size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("industry")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Location"
                    placeholder="Enter company location"
                    leftSection={
                      <FaMapMarkerAlt size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("location")}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Text size="sm" fw={500} className="mb-2">
                    Company Description
                  </Text>
                  <Textarea
                    placeholder="Describe your company"
                    rows={6}
                    {...form.getInputProps("companyDescription")}
                  />
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>

          {/* Social Media Tab */}
          <Tabs.Panel value="social" pt="md">
            <Card
              withBorder
              radius="md"
              className={useThemeClasses(
                "p-6 shadow-sm",
                "p-6 shadow-dark-sm bg-dark-7",
              )}
            >
              <Title
                order={3}
                className={useThemeClasses(
                  "mb-4 text-xl font-semibold text-blue-800",
                  "mb-4 text-xl font-semibold text-blue-300",
                )}
              >
                Social Media Links
              </Title>

              <Text
                className={useThemeClasses(
                  "mb-4 text-gray-600",
                  "mb-4 text-gray-400",
                )}
              >
                Add your company&apos;s social media profiles to increase
                visibility and connect with candidates.
              </Text>

              <Grid gutter="md">
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="LinkedIn"
                    placeholder="Enter LinkedIn profile URL"
                    leftSection={
                      <FaLinkedin size={16} className="text-blue-500" />
                    }
                    {...form.getInputProps("linkedin")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Twitter"
                    placeholder="Enter Twitter profile URL"
                    leftSection={
                      <FaTwitter size={16} className="text-cyan-500" />
                    }
                    {...form.getInputProps("twitter")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Facebook"
                    placeholder="Enter Facebook profile URL"
                    leftSection={
                      <FaFacebook size={16} className="text-indigo-500" />
                    }
                    {...form.getInputProps("facebook")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Instagram"
                    placeholder="Enter Instagram profile URL"
                    leftSection={
                      <FaInstagram size={16} className="text-pink-500" />
                    }
                    {...form.getInputProps("instagram")}
                  />
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>

          {/* Company Details Tab */}
          <Tabs.Panel value="details" pt="md">
            <Card
              withBorder
              radius="md"
              className={useThemeClasses(
                "p-6 shadow-sm",
                "p-6 shadow-dark-sm bg-dark-7",
              )}
            >
              <Title
                order={3}
                className={useThemeClasses(
                  "mb-4 text-xl font-semibold text-blue-800",
                  "mb-4 text-xl font-semibold text-blue-300",
                )}
              >
                Company Details
              </Title>

              <Text
                className={useThemeClasses(
                  "mb-4 text-gray-600",
                  "mb-4 text-gray-400",
                )}
              >
                These details help candidates understand more about your company
                size and type.
              </Text>

              <Grid gutter="md">
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Select
                    label="Company Size"
                    placeholder="Select company size"
                    data={[
                      { value: "1-10", label: "1-10 employees" },
                      { value: "11-50", label: "11-50 employees" },
                      { value: "51-200", label: "51-200 employees" },
                      { value: "201-500", label: "201-500 employees" },
                      { value: "501+", label: "501+ employees" },
                    ]}
                    leftSection={
                      <FaBriefcase size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("companySize")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Select
                    label="Company Type"
                    placeholder="Select company type"
                    data={[
                      { value: "startup", label: "Startup" },
                      {
                        value: "sme",
                        label: "Small & Medium Enterprise (SME)",
                      },
                      { value: "corporate", label: "Corporate" },
                      { value: "non-profit", label: "Non-Profit" },
                      { value: "government", label: "Government" },
                    ]}
                    leftSection={
                      <FaBuilding size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("companyType")}
                  />
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>
        </Tabs>

        <Group justify="flex-end" mt="xl">
          <Button type="submit" size="md" leftSection={<FaSave size={16} />}>
            Save All Changes
          </Button>
        </Group>
      </form>

      {/* Company Logo Modal */}
      <Modal
        opened={logoModalOpened}
        onClose={closeLogoModal}
        title="Update Company Logo"
        centered
      >
        <div className="mb-6 flex flex-col items-center">
          <Avatar
            size={150}
            radius="md"
            color="blue"
            className="mb-4 border-4 border-white shadow-md"
            src={companyLogoPreview}
          >
            {!companyLogoPreview && form.values.companyName.charAt(0)}
          </Avatar>

          <FileInput
            label="Upload new company logo"
            placeholder="Click to select an image"
            accept="image/png,image/jpeg,image/jpg"
            className="mb-4 w-full"
            onChange={handleLogoChange}
            leftSection={<FaUpload size={16} />}
            clearable
          />

          <Text size="sm" c="dimmed" className="mb-4 text-center">
            Recommended: Square image, at least 400x400 pixels
          </Text>

          <Group justify="center" className="w-full">
            <Button variant="default" onClick={closeLogoModal}>
              Cancel
            </Button>
            {companyLogoPreview && (
              <Button color="red" onClick={handleRemoveLogo}>
                Remove Logo
              </Button>
            )}
          </Group>
        </div>
      </Modal>
    </PageContainer>
  );
}
