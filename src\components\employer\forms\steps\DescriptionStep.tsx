import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { Box, Divider, TagsInput, Text, Textarea, Tooltip } from "@mantine/core";
import { FaFileAlt, FaInfoCircle, FaTags } from "react-icons/fa";

interface DescriptionStepProps {
  form: any;
}

export default function DescriptionStep({ form }: DescriptionStepProps) {
  return (
    <div className="space-y-8">
      <div
        className={useThemeClasses(
          "border-b border-gray-200 pb-4",
          "border-b border-dark-4 pb-4",
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaFileAlt size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Job Description & Requirements
            </Text>
          </div>
          <Text
            size="sm"
            className={useThemeClasses(
              "rounded-md bg-gray-100 px-2 py-1 text-gray-600",
              "rounded-md bg-dark-5 px-2 py-1 text-gray-300",
            )}
          >
            * Required fields
          </Text>
        </div>
        <Text size="sm" c="dimmed" mt="xs">
          Provide detailed information about the job responsibilities and
          requirements
        </Text>
      </div>

      <Textarea
        label={
          <div className="flex items-center gap-1">
            <Text className="font-medium">Job Description</Text>
            <Text c="red" span>
              *
            </Text>
            <Tooltip label="Describe the role, responsibilities, and what a typical day looks like">
              <FaInfoCircle size={14} className="cursor-help text-gray-400" />
            </Tooltip>
          </div>
        }
        placeholder="Enter a detailed job description..."
        rows={8}
        {...form.getInputProps("jobDescription")}
        error={form.errors.jobDescription}
        aria-label="Job Description"
        size="md"
        className="mb-6"
        radius="md"
      />

      <Textarea
        label={
          <div className="flex items-center gap-1">
            <Text>Job Requirements</Text>
            <Text c="red" span>
              *
            </Text>
            <Tooltip label="List skills, qualifications, and experience needed for this role">
              <FaInfoCircle size={14} className="cursor-help text-gray-400" />
            </Tooltip>
          </div>
        }
        placeholder="Enter job requirements..."
        rows={8}
        {...form.getInputProps("jobRequirements")}
        error={form.errors.jobRequirements}
        aria-label="Job Requirements"
        size="md"
      />

      <Divider my="lg" />

      <Box className="mt-6">
        <Text className="text-primary-color mb-3 text-lg font-semibold">
          Tags & Keywords
        </Text>
        <Text size="sm" c="dimmed" mb="md">
          Add relevant tags to help candidates find your job posting
        </Text>

        <TagsInput
          label="Job Tags"
          placeholder="Add tags (e.g., React, Python, Marketing) and press Enter"
          leftSection={<FaTags size={18} className="text-primary-color" />}
          {...form.getInputProps("jobTags")}
          error={form.errors.jobTags}
          aria-label="Job Tags"
          size="md"
        />
      </Box>
    </div>
  );
}
