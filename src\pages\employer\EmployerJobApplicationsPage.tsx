// We're using our own dummy data instead of these imports
// import {
//   getApplicationsByJobId,
//   getCandidateById,
// } from "@/data/job-applications";
import { dummyJobs } from "@/data/jobs-data";
import { PageContainer } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { type Candidate } from "@/types";
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Card,
  Checkbox,
  Divider,
  Grid,
  Group,
  Menu,
  Paper,
  Progress,
  SegmentedControl,
  Select,
  Stack,
  Tabs,
  Text,
  TextInput,
  Title,
  Tooltip,
} from "@mantine/core";
import { useEffect, useMemo, useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaCheckCircle,
  FaDownload,
  FaEllipsisV,
  FaEnvelope,
  FaEye,
  FaFilter,
  FaHourglassHalf,
  FaListUl,
  FaMapMarkerAlt,
  <PERSON>aSearch,
  FaThLarge,
  FaTimesCircle,
  FaUser<PERSON>heck,
  FaUserTimes,
  FaUsers,
} from "react-icons/fa";
import { useNavigate, useParams } from "react-router";

// Define types for our dummy data
interface JobApplication {
  id: number;
  jobId: number;
  candidateId: number;
  jobTitle: string;
  company: string;
  appliedDate: string;
  status: string; // More flexible to avoid type issues
  jobType: string;
  location: string;
}

interface JobCandidate extends Candidate {
  resumeUrl?: string;
  coverLetter?: string;
  skills?: string[];
  experience?: {
    company: string;
    role: string;
    duration: string;
  }[];
  education?: {
    institution: string;
    degree: string;
    duration: string;
  }[];
}

export default function EmployerJobApplicationsPage() {
  const navigate = useNavigate();
  const params = useParams();
  const jobId = Number(params.jobId);

  // Create a dummy job if one doesn't exist with the current ID
  const dummyJob = {
    id: jobId,
    title: "Senior Software Engineer",
    company: "Tech Innovations Inc.",
    location: "San Francisco, CA",
    type: "Full-time",
    workType: "Hybrid",
    salary: "$120,000 - $150,000",
    experience: "5+ years",
    skills: ["React", "TypeScript", "Node.js", "AWS"],
    datePosted: "2024-05-01",
    careerLevel: "Senior",
    category: "Software Development",
    description:
      "We are looking for a Senior Software Engineer to join our growing team. The ideal candidate will have strong experience with modern web technologies and cloud platforms.",
  };

  // Use the dummy job if one doesn't exist with the current ID
  const [job] = useState(dummyJobs.find((j) => j.id === jobId) || dummyJob);

  // We're using our own dummy applications instead of this
  // const [applications] = useState(getApplicationsByJobId(jobId));
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards");
  const [selectedApplications, setSelectedApplications] = useState<number[]>(
    [],
  );

  // Filter applications based on search and filter criteria
  // Dummy applications data for the current job
  const dummyApplications = useMemo(
    () => [
      {
        id: 101,
        jobId: jobId,
        candidateId: 201,
        jobTitle: job?.title || "Software Engineer",
        company: job?.company || "Tech Company",
        appliedDate: "2024-05-01",
        status: "pending",
        jobType: job?.type || "Full-time",
        location: job?.location || "Remote",
      },
      {
        id: 102,
        jobId: jobId,
        candidateId: 202,
        jobTitle: job?.title || "Software Engineer",
        company: job?.company || "Tech Company",
        appliedDate: "2024-05-02",
        status: "reviewed",
        jobType: job?.type || "Full-time",
        location: job?.location || "Remote",
      },
      {
        id: 103,
        jobId: jobId,
        candidateId: 203,
        jobTitle: job?.title || "Software Engineer",
        company: job?.company || "Tech Company",
        appliedDate: "2024-05-03",
        status: "accepted",
        jobType: job?.type || "Full-time",
        location: job?.location || "Remote",
      },
      {
        id: 104,
        jobId: jobId,
        candidateId: 204,
        jobTitle: job?.title || "Software Engineer",
        company: job?.company || "Tech Company",
        appliedDate: "2024-05-04",
        status: "rejected",
        jobType: job?.type || "Full-time",
        location: job?.location || "Remote",
      },
      {
        id: 105,
        jobId: jobId,
        candidateId: 205,
        jobTitle: job?.title || "Software Engineer",
        company: job?.company || "Tech Company",
        appliedDate: "2024-05-05",
        status: "pending",
        jobType: job?.type || "Full-time",
        location: job?.location || "Remote",
      },
    ],
    [jobId, job],
  );

  // Dummy candidates data corresponding to the applications
  const dummyCandidates = useMemo(
    () => [
      {
        id: 201,
        name: "Alex Johnson",
        email: "<EMAIL>",
        status: "Applied",
        jobId: jobId,
        appliedDate: "2024-05-01",
        skills: ["JavaScript", "React", "TypeScript", "Node.js"],
        resumeUrl: "/resumes/alex-resume.pdf",
        coverLetter: "I am excited to apply for this position...",
        experience: [
          {
            company: "Previous Tech",
            role: "Junior Developer",
            duration: "2021-2023",
          },
        ],
        education: [
          {
            institution: "Tech University",
            degree: "BS in Computer Science",
            duration: "2017-2021",
          },
        ],
      },
      {
        id: 202,
        name: "Taylor Smith",
        email: "<EMAIL>",
        status: "Shortlisted",
        jobId: jobId,
        appliedDate: "2024-05-02",
        skills: ["React", "Redux", "CSS", "HTML", "UI/UX"],
        resumeUrl: "/resumes/taylor-resume.pdf",
        experience: [
          {
            company: "Design Agency",
            role: "Frontend Developer",
            duration: "2020-2024",
          },
        ],
      },
      {
        id: 203,
        name: "Jordan Lee",
        email: "<EMAIL>",
        status: "Interviewed",
        jobId: jobId,
        appliedDate: "2024-05-03",
        skills: ["JavaScript", "React", "Node.js", "MongoDB", "Express"],
        resumeUrl: "/resumes/jordan-resume.pdf",
        coverLetter:
          "With my full-stack experience, I believe I can contribute...",
        experience: [
          {
            company: "Startup Inc",
            role: "Full Stack Developer",
            duration: "2019-2024",
          },
        ],
      },
      {
        id: 204,
        name: "Casey Brown",
        email: "<EMAIL>",
        status: "Rejected",
        jobId: jobId,
        appliedDate: "2024-05-04",
        skills: ["JavaScript", "Angular", "Java"],
        resumeUrl: "/resumes/casey-resume.pdf",
      },
      {
        id: 205,
        name: "Riley Garcia",
        email: "<EMAIL>",
        status: "Applied",
        jobId: jobId,
        appliedDate: "2024-05-05",
        skills: ["React", "Next.js", "TypeScript", "Tailwind CSS"],
        resumeUrl: "/resumes/riley-resume.pdf",
        experience: [
          {
            company: "Web Solutions",
            role: "Frontend Developer",
            duration: "2022-2024",
          },
        ],
        education: [
          {
            institution: "State University",
            degree: "MS in Web Development",
            duration: "2020-2022",
          },
        ],
      },
    ],
    [jobId],
  );

  // Function to get candidate by ID from our dummy data
  const getCandidateById = (id: number): JobCandidate | undefined => {
    return dummyCandidates.find(
      (candidate: JobCandidate) => candidate.id === id,
    );
  };

  // Use our dummy applications instead of the ones from the data file
  const [filteredApplications, setFilteredApplications] =
    useState(dummyApplications);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string | null>("all");

  // Calculate statistics
  const totalApplications = dummyApplications.length;
  const pendingApplications = dummyApplications.filter(
    (app: JobApplication) => app.status === "pending",
  ).length;
  const reviewedApplications = dummyApplications.filter(
    (app: JobApplication) => app.status === "reviewed",
  ).length;
  const acceptedApplications = dummyApplications.filter(
    (app: JobApplication) => app.status === "accepted",
  ).length;
  const rejectedApplications = dummyApplications.filter(
    (app: JobApplication) => app.status === "rejected",
  ).length;

  useEffect(() => {
    // Apply filters
    let filtered = dummyApplications;

    // Filter by tab
    if (activeTab !== "all") {
      filtered = filtered.filter(
        (app: JobApplication) => app.status === activeTab,
      );
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter((app: JobApplication) => {
        const candidate = dummyCandidates.find(
          (c: JobCandidate) => c.id === app.candidateId,
        );
        return (
          app.jobTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (candidate &&
            candidate.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (candidate &&
            candidate.email.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      });
    }

    // Apply status filter (if not already filtered by tab)
    if (statusFilter && activeTab === "all") {
      filtered = filtered.filter(
        (app: JobApplication) => app.status === statusFilter,
      );
    }

    setFilteredApplications(filtered);
  }, [
    dummyApplications,
    dummyCandidates,
    searchQuery,
    statusFilter,
    activeTab,
    jobId,
  ]);

  const handleSelectApplication = (id: number) => {
    if (selectedApplications.includes(id)) {
      setSelectedApplications(
        selectedApplications.filter((appId) => appId !== id),
      );
    } else {
      setSelectedApplications([...selectedApplications, id]);
    }
  };

  const handleSelectAll = () => {
    if (selectedApplications.length === filteredApplications.length) {
      setSelectedApplications([]);
    } else {
      setSelectedApplications(
        filteredApplications.map((app: JobApplication) => app.id),
      );
    }
  };

  const handleBulkAction = (action: "shortlist" | "reject" | "download") => {
    // In a real app, you would implement the actual actions here
    console.log(`Performing ${action} on applications:`, selectedApplications);
    // Reset selection after action
    setSelectedApplications([]);
  };

  const handleViewCandidate = (candidateId: number) => {
    navigate(`/employer/candidates/${candidateId}`);
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Manage Jobs", href: "/employer/manage-jobs" },
        { title: job?.title || "Job Applications" },
      ]}
      variant="employer"
    >
      {/* Job Overview Section */}
      <Card withBorder radius="md" className="mb-8 overflow-hidden">
        <div className="relative">
          {/* Background gradient */}
          <div
            className={useThemeClasses(
              "absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50",
              "absolute inset-0 bg-gradient-to-r from-dark-6 to-dark-5",
            )}
          ></div>

          <div className="relative p-6 md:p-8">
            <Grid>
              <Grid.Col span={{ base: 12, md: 8 }}>
                <Group align="flex-start" className="mb-4">
                  <Avatar
                    size={80}
                    radius="md"
                    color="blue"
                    className="border-4 border-white shadow-md"
                  >
                    {job?.company.substring(0, 2).toUpperCase()}
                  </Avatar>

                  <div>
                    <Title order={1} className="text-2xl font-bold">
                      {job?.title}
                    </Title>

                    <Group gap="xs" className="mt-1">
                      <Group gap="xs">
                        <FaBuilding className="text-blue-500" size={14} />
                        <Text
                          size="sm"
                          className={useThemeClasses(
                            "text-gray-700",
                            "text-gray-300",
                          )}
                        >
                          {job?.company}
                        </Text>
                      </Group>

                      <Divider orientation="vertical" />

                      <Group gap="xs">
                        <FaMapMarkerAlt className="text-blue-500" size={14} />
                        <Text
                          size="sm"
                          className={useThemeClasses(
                            "text-gray-700",
                            "text-gray-300",
                          )}
                        >
                          {job?.location}
                        </Text>
                      </Group>

                      <Divider orientation="vertical" />

                      <Group gap="xs">
                        <FaBriefcase className="text-blue-500" size={14} />
                        <Text
                          size="sm"
                          className={useThemeClasses(
                            "text-gray-700",
                            "text-gray-300",
                          )}
                        >
                          {job?.type}
                        </Text>
                      </Group>
                    </Group>

                    <Group gap="md" className="mt-4">
                      <Badge color="blue" size="lg">
                        {job?.workType}
                      </Badge>
                      <Badge color="cyan" size="lg">
                        {job?.salary}
                      </Badge>
                      <Badge color="indigo" size="lg">
                        {job?.experience} Experience
                      </Badge>
                    </Group>
                  </div>
                </Group>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 4 }}>
                <Card
                  withBorder
                  radius="md"
                  className={useThemeClasses(
                    "bg-white shadow-sm",
                    "bg-dark-6 shadow-dark-lg",
                  )}
                >
                  <Title order={4} className="mb-3 text-center">
                    Applications Summary
                  </Title>

                  <Group justify="space-between" className="mb-2">
                    <Text size="sm">Total Applications</Text>
                    <Badge size="lg" color="blue">
                      {totalApplications}
                    </Badge>
                  </Group>

                  {/* Using multiple progress bars instead of sections */}
                  <div className="mb-3">
                    <Progress
                      value={(pendingApplications / totalApplications) * 100}
                      color="yellow"
                      size="xl"
                      className="mb-1"
                    />
                    <Progress
                      value={(reviewedApplications / totalApplications) * 100}
                      color="blue"
                      size="xl"
                      className="mb-1"
                    />
                    <Progress
                      value={(acceptedApplications / totalApplications) * 100}
                      color="green"
                      size="xl"
                      className="mb-1"
                    />
                    <Progress
                      value={(rejectedApplications / totalApplications) * 100}
                      color="red"
                      size="xl"
                    />
                  </div>

                  <Group justify="space-between" className="text-sm">
                    <Group gap="xs">
                      <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                      <Text size="xs">Pending ({pendingApplications})</Text>
                    </Group>

                    <Group gap="xs">
                      <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                      <Text size="xs">Reviewed ({reviewedApplications})</Text>
                    </Group>

                    <Group gap="xs">
                      <div className="h-3 w-3 rounded-full bg-green-500"></div>
                      <Text size="xs">Accepted ({acceptedApplications})</Text>
                    </Group>

                    <Group gap="xs">
                      <div className="h-3 w-3 rounded-full bg-red-500"></div>
                      <Text size="xs">Rejected ({rejectedApplications})</Text>
                    </Group>
                  </Group>
                </Card>
              </Grid.Col>
            </Grid>
          </div>
        </div>
      </Card>

      {/* Tabs and Controls */}
      <div className="mb-6">
        <Grid>
          <Grid.Col span={{ base: 12, md: 8 }}>
            <Tabs value={activeTab} onChange={setActiveTab}>
              <Tabs.List>
                <Tabs.Tab value="all" leftSection={<FaUsers size={16} />}>
                  All Applications ({totalApplications})
                </Tabs.Tab>
                <Tabs.Tab
                  value="pending"
                  leftSection={<FaHourglassHalf size={16} />}
                >
                  Pending ({pendingApplications})
                </Tabs.Tab>
                <Tabs.Tab value="reviewed" leftSection={<FaEye size={16} />}>
                  Reviewed ({reviewedApplications})
                </Tabs.Tab>
                <Tabs.Tab
                  value="accepted"
                  leftSection={<FaCheckCircle size={16} />}
                >
                  Accepted ({acceptedApplications})
                </Tabs.Tab>
                <Tabs.Tab
                  value="rejected"
                  leftSection={<FaTimesCircle size={16} />}
                >
                  Rejected ({rejectedApplications})
                </Tabs.Tab>
              </Tabs.List>
            </Tabs>
          </Grid.Col>

          <Grid.Col
            span={{ base: 12, md: 4 }}
            className="flex items-center justify-end"
          >
            <Group>
              <SegmentedControl
                value={viewMode}
                onChange={(value) => setViewMode(value as "cards" | "table")}
                data={[
                  {
                    value: "cards",
                    label: (
                      <div className="flex items-center gap-2">
                        <FaThLarge size={16} />
                        <span>Cards</span>
                      </div>
                    ),
                  },
                  {
                    value: "table",
                    label: (
                      <div className="flex items-center gap-2">
                        <FaListUl size={16} />
                        <span>Table</span>
                      </div>
                    ),
                  },
                ]}
              />
            </Group>
          </Grid.Col>
        </Grid>
      </div>

      {/* Search and Filter Bar */}
      <Paper withBorder p="md" radius="md" className="mb-6">
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <TextInput
              placeholder="Search by candidate name, email or job title"
              leftSection={<FaSearch size={16} className="text-gray-500" />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 3 }}>
            <Select
              placeholder="Filter by status"
              data={[
                { value: "pending", label: "Pending" },
                { value: "reviewed", label: "Reviewed" },
                { value: "accepted", label: "Accepted" },
                { value: "rejected", label: "Rejected" },
              ]}
              leftSection={<FaFilter size={16} className="text-gray-500" />}
              value={statusFilter}
              onChange={setStatusFilter}
              clearable
              disabled={activeTab !== "all"}
            />
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 3 }}>
            <Group justify="flex-end">
              {selectedApplications.length > 0 && (
                <Menu position="bottom-end" shadow="md">
                  <Menu.Target>
                    <div className="cursor-pointer">
                      <Button variant="light">
                        Bulk Actions ({selectedApplications.length})
                      </Button>
                    </div>
                  </Menu.Target>
                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<FaUserCheck size={14} />}
                      onClick={() => handleBulkAction("shortlist")}
                    >
                      Shortlist Selected
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<FaUserTimes size={14} />}
                      onClick={() => handleBulkAction("reject")}
                      color="red"
                    >
                      Reject Selected
                    </Menu.Item>
                    <Menu.Divider />
                    <Menu.Item
                      leftSection={<FaDownload size={14} />}
                      onClick={() => handleBulkAction("download")}
                    >
                      Download Resumes
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              )}
            </Group>
          </Grid.Col>
        </Grid>
      </Paper>

      {/* Applications List */}
      {filteredApplications.length === 0 ? (
        <Paper withBorder p="xl" radius="md" className="text-center">
          <Title order={3} className="mb-2 text-gray-700">
            No applications found
          </Title>
          <Text className="text-gray-500">
            Try adjusting your search or filter criteria
          </Text>
        </Paper>
      ) : viewMode === "cards" ? (
        <div className="mb-6">
          <Grid>
            {filteredApplications.map((application: JobApplication) => {
              const candidate = getCandidateById(application.candidateId);
              if (!candidate) return null;

              return (
                <Grid.Col
                  key={application.id}
                  span={{ base: 12, sm: 6, lg: 4 }}
                >
                  <Card
                    withBorder
                    shadow="sm"
                    radius="md"
                    padding="md"
                    className="h-full"
                  >
                    <Card.Section className="relative bg-gradient-to-r from-blue-50 to-indigo-50 p-3">
                      <Checkbox
                        checked={selectedApplications.includes(application.id)}
                        onChange={() => handleSelectApplication(application.id)}
                        className="absolute top-3 right-3 z-10"
                      />

                      <Group justify="space-between" className="mb-2">
                        <Group>
                          <Avatar radius="xl" color="blue">
                            {candidate.name.substring(0, 2).toUpperCase()}
                          </Avatar>
                          <div>
                            <Text fw={600}>{candidate.name}</Text>
                            <Text size="xs" c="dimmed">
                              {candidate.email}
                            </Text>
                          </div>
                        </Group>
                      </Group>
                    </Card.Section>

                    <Stack gap="xs" mt="md">
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Application Status
                        </Text>
                        <Badge
                          color={
                            application.status === "pending"
                              ? "yellow"
                              : application.status === "reviewed"
                                ? "blue"
                                : application.status === "accepted"
                                  ? "green"
                                  : "red"
                          }
                        >
                          {application.status.charAt(0).toUpperCase() +
                            application.status.slice(1)}
                        </Badge>
                      </Group>

                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Candidate Status
                        </Text>
                        <Text size="sm">{candidate.status}</Text>
                      </Group>

                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Applied Date
                        </Text>
                        <Text size="sm">
                          {new Date(
                            application.appliedDate,
                          ).toLocaleDateString()}
                        </Text>
                      </Group>

                      {candidate.skills && candidate.skills.length > 0 && (
                        <div className="mt-3 mb-5">
                          <Text size="sm" c="dimmed" mb={5}>
                            Skills
                          </Text>
                          <Group>
                            {candidate.skills
                              ?.slice(0, 3)
                              .map((skill: string, index: number) => (
                                <Badge
                                  key={index}
                                  color="blue"
                                  variant="light"
                                  size="sm"
                                >
                                  {skill}
                                </Badge>
                              ))}
                            {candidate.skills &&
                              candidate.skills.length > 3 && (
                                <Badge color="gray" variant="light" size="sm">
                                  +{candidate.skills.length - 3} more
                                </Badge>
                              )}
                          </Group>
                        </div>
                      )}
                    </Stack>

                    <Group justify="space-between" mt="md" className="!mt-auto">
                      <Button
                        variant="light"
                        color="blue"
                        leftSection={<FaEye size={14} />}
                        onClick={() => handleViewCandidate(candidate.id)}
                        fullWidth
                      >
                        View Details
                      </Button>
                    </Group>
                  </Card>
                </Grid.Col>
              );
            })}
          </Grid>
        </div>
      ) : (
        <Paper withBorder radius="md" className="mb-6 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="p-3 text-left">
                    <Checkbox
                      checked={
                        selectedApplications.length ===
                          filteredApplications.length &&
                        filteredApplications.length > 0
                      }
                      indeterminate={
                        selectedApplications.length > 0 &&
                        selectedApplications.length <
                          filteredApplications.length
                      }
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th className="p-3 text-left">Candidate</th>
                  <th className="p-3 text-left">Applied Date</th>
                  <th className="p-3 text-left">Skills</th>
                  <th className="p-3 text-left">Status</th>
                  <th className="p-3 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredApplications.map((application: JobApplication) => {
                  const candidate = getCandidateById(application.candidateId);
                  if (!candidate) return null;

                  return (
                    <tr
                      key={application.id}
                      className="border-t border-gray-200 hover:bg-gray-50"
                    >
                      <td className="p-3">
                        <Checkbox
                          checked={selectedApplications.includes(
                            application.id,
                          )}
                          onChange={() =>
                            handleSelectApplication(application.id)
                          }
                        />
                      </td>
                      <td className="p-3">
                        <Group>
                          <Avatar radius="xl" color="blue" size="sm">
                            {candidate.name.substring(0, 2).toUpperCase()}
                          </Avatar>
                          <div>
                            <Text size="sm" fw={500}>
                              {candidate.name}
                            </Text>
                            <Text size="xs" c="dimmed">
                              {candidate.email}
                            </Text>
                          </div>
                        </Group>
                      </td>
                      <td className="p-3">
                        <Text size="sm">
                          {new Date(
                            application.appliedDate,
                          ).toLocaleDateString()}
                        </Text>
                      </td>
                      <td className="p-3">
                        {candidate.skills && candidate.skills.length > 0 ? (
                          <Group>
                            {candidate.skills
                              ?.slice(0, 2)
                              .map((skill: string, index: number) => (
                                <Badge
                                  key={index}
                                  color="blue"
                                  variant="light"
                                  size="sm"
                                >
                                  {skill}
                                </Badge>
                              ))}
                            {candidate.skills &&
                              candidate.skills.length > 2 && (
                                <Badge color="gray" variant="light" size="sm">
                                  +{candidate.skills.length - 2} more
                                </Badge>
                              )}
                          </Group>
                        ) : (
                          <Text size="sm" c="dimmed">
                            No skills listed
                          </Text>
                        )}
                      </td>
                      <td className="p-3">
                        <Badge
                          color={
                            application.status === "pending"
                              ? "yellow"
                              : application.status === "reviewed"
                                ? "blue"
                                : application.status === "accepted"
                                  ? "green"
                                  : "red"
                          }
                        >
                          {application.status.charAt(0).toUpperCase() +
                            application.status.slice(1)}
                        </Badge>
                      </td>
                      <td className="p-3">
                        <Group>
                          <Tooltip label="View Details">
                            <ActionIcon
                              color="blue"
                              variant="light"
                              onClick={() => handleViewCandidate(candidate.id)}
                            >
                              <FaEye size={16} />
                            </ActionIcon>
                          </Tooltip>

                          <Menu position="bottom-end" shadow="md">
                            <Menu.Target>
                              <ActionIcon variant="subtle">
                                <FaEllipsisV size={16} />
                              </ActionIcon>
                            </Menu.Target>
                            <Menu.Dropdown>
                              <Menu.Item
                                leftSection={<FaUserCheck size={14} />}
                              >
                                Shortlist
                              </Menu.Item>
                              <Menu.Item
                                leftSection={<FaUserTimes size={14} />}
                                color="red"
                              >
                                Reject
                              </Menu.Item>
                              <Menu.Divider />
                              <Menu.Item leftSection={<FaDownload size={14} />}>
                                Download Resume
                              </Menu.Item>
                              <Menu.Item leftSection={<FaEnvelope size={14} />}>
                                Contact Candidate
                              </Menu.Item>
                            </Menu.Dropdown>
                          </Menu>
                        </Group>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </Paper>
      )}
    </PageContainer>
  );
}
